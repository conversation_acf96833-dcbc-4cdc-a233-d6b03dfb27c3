#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查数据结构
"""

import pandas as pd
import numpy as np

def check_data_structure():
    df = pd.read_parquet('result_date_part01/batch_001.parquet')
    
    print('数据类型详情:')
    for col in df.columns:
        print(f'\n{col}: {df[col].dtype}')
        sample = df[col].iloc[0]
        print(f'  示例值: {sample}')
        print(f'  类型: {type(sample)}')
        
        # 检查是否有列表类型的数据
        non_null_values = df[col].dropna()
        if len(non_null_values) > 0:
            first_non_null = non_null_values.iloc[0]
            print(f'  第一个非空值: {first_non_null}')
            print(f'  第一个非空值类型: {type(first_non_null)}')
            
            # 检查是否是列表
            if isinstance(first_non_null, list):
                print(f'  列表长度: {len(first_non_null)}')
                if len(first_non_null) > 0:
                    print(f'  列表第一个元素: {first_non_null[0]}')
                    print(f'  列表第一个元素类型: {type(first_non_null[0])}')

if __name__ == "__main__":
    check_data_structure()
