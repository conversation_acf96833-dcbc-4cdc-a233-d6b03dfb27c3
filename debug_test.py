#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试测试脚本
"""

import pandas as pd
import numpy as np

def debug_data():
    df = pd.read_parquet('result_date_part01/batch_001.parquet')
    
    # 只取前5行进行测试
    df_small = df.head(5)
    
    print("测试数据:")
    for col in df_small.columns:
        print(f"\n列名: {col}")
        for i, value in enumerate(df_small[col]):
            print(f"  行{i}: {value} (类型: {type(value)})")
            if isinstance(value, np.ndarray):
                print(f"    数组形状: {value.shape}")
                print(f"    数组内容: {list(value)}")

if __name__ == "__main__":
    debug_data()
