#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Parquet文件数据处理脚本 - result_date_part02（快速版本）
处理result_date_part02文件夹下的所有.parquet文件，并输出到result_date_part02_update文件夹
"""

import os
import re
import pandas as pd
from tqdm import tqdm
import glob


def convert_punctuation_and_case(value):
    """转换标点符号和字母大小写"""
    try:
        if pd.isna(value) or value == '' or value is None:
            return value
        
        # 如果是列表或数组，处理每个元素
        if isinstance(value, (list, tuple)) or hasattr(value, '__iter__') and not isinstance(value, str):
            try:
                result = []
                for item in value:
                    if pd.isna(item) or item == '' or item is None:
                        continue
                    item_str = str(item)
                    item_str = item_str.replace(';', '；').replace('(', '（').replace(')', '）').lower()
                    result.append(item_str)
                return result
            except:
                pass
        
        # 字符串处理
        text = str(value)
        text = text.replace(';', '；').replace('(', '（').replace(')', '）').lower()
        return text
    except Exception:
        return value


def remove_exclusion_statements(value):
    """删除括号包裹的排除性语句"""
    try:
        if pd.isna(value) or value == '' or value is None:
            return value
        
        exclusion_patterns = [
            r'（不含[^）]*?）', r'（不包含[^）]*?）', r'（除[^）]*?）',
            r'（不包括[^）]*?）', r'（不得[^）]*?）'
        ]
        
        # 如果是列表或数组，处理每个元素
        if isinstance(value, (list, tuple)) or hasattr(value, '__iter__') and not isinstance(value, str):
            try:
                result = []
                for item in value:
                    if pd.isna(item) or item == '' or item is None:
                        continue
                    item_str = str(item)
                    for pattern in exclusion_patterns:
                        item_str = re.sub(pattern, '', item_str)
                    item_str = item_str.strip()
                    if item_str:
                        result.append(item_str)
                return result
            except:
                pass
        
        # 字符串处理
        text = str(value)
        for pattern in exclusion_patterns:
            text = re.sub(pattern, '', text)
        return text.strip()
    except Exception:
        return value


def split_company_profile(value):
    """将company_profile字段按全角分号分割成列表"""
    try:
        if pd.isna(value) or value == '' or value is None:
            return []
        
        # 如果已经是列表或数组，先处理每个元素，然后合并分割
        if isinstance(value, (list, tuple)) or hasattr(value, '__iter__') and not isinstance(value, str):
            try:
                all_items = []
                for item in value:
                    if pd.isna(item) or item == '' or item is None:
                        continue
                    item_str = str(item)
                    if '；' in item_str:
                        items = item_str.split('；')
                    else:
                        items = [item_str]
                    
                    for sub_item in items:
                        sub_item = sub_item.strip()
                        if sub_item:
                            all_items.append(sub_item)
                
                return all_items
            except:
                pass
        
        # 字符串处理
        text = str(value)
        if '；' in text:
            items = text.split('；')
        else:
            items = [text]
        
        items = [item.strip() for item in items if item.strip()]
        return items
    except Exception:
        return []


def process_dataframe(df):
    """处理DataFrame中的数据"""
    try:
        excluded_columns = ['company_status_clean', 'industry_l1_code', 'lc_company_id', 'company_name']
        columns_to_process = [col for col in df.columns if col not in excluded_columns]
        
        df_processed = df.copy()
        
        # 1. 标点符号和字母转换
        for col in columns_to_process:
            if col in df_processed.columns:
                df_processed[col] = df_processed[col].apply(convert_punctuation_and_case)
        
        # 2. 删除排除性语句（仅针对company_profile）
        if 'company_profile' in df_processed.columns:
            df_processed['company_profile'] = df_processed['company_profile'].apply(remove_exclusion_statements)
        
        # 3. 分割company_profile字段
        if 'company_profile' in df_processed.columns:
            df_processed['company_profile'] = df_processed['company_profile'].apply(split_company_profile)
        
        return df_processed
    
    except Exception as e:
        print(f"    处理DataFrame时出错: {str(e)}")
        return df


def main():
    """主函数"""
    input_folder = "result_date_part02"
    output_folder = "result_date_part02_update"
    
    if not os.path.exists(input_folder):
        print(f"错误：输入文件夹 '{input_folder}' 不存在！")
        return
    
    os.makedirs(output_folder, exist_ok=True)
    print(f"输出文件夹已创建: {output_folder}")
    
    parquet_files = glob.glob(os.path.join(input_folder, "*.parquet"))
    
    if not parquet_files:
        print(f"在 '{input_folder}' 文件夹中未找到任何 .parquet 文件！")
        return
    
    print(f"找到 {len(parquet_files)} 个 .parquet 文件")
    
    # 处理每个文件
    for file_path in tqdm(parquet_files, desc="处理文件"):
        try:
            file_name = os.path.basename(file_path)
            output_path = os.path.join(output_folder, file_name)
            
            # 读取、处理、保存
            df = pd.read_parquet(file_path)
            df_processed = process_dataframe(df)
            df_processed.to_parquet(output_path, index=False)
            
        except Exception as e:
            print(f"\n处理文件 {file_path} 时出错: {str(e)}")
            continue
    
    print(f"\n所有文件处理完成！结果保存在 '{output_folder}' 文件夹中。")


if __name__ == "__main__":
    main()
