"""
企业竞品业务信息提取脚本
从t_product_competitor数据中提取目标企业的竞品业务信息
"""

import pandas as pd
import os
import gc
from pathlib import Path

def main():
    # 1. 读取目标企业基本信息
    print("正在读取企业基本信息...")
    company_basic_path = "company_business_scope_output/company_basic_info"
    company_dfs = []
    
    for file in os.listdir(company_basic_path):
        if file.endswith('.parquet'):
            df = pd.read_parquet(os.path.join(company_basic_path, file))
            company_dfs.append(df[['lc_company_id']])
    
    company_df = pd.concat(company_dfs, ignore_index=True)
    print(f"读取到 {len(company_df)} 条企业基本信息")
    
    # 创建目标企业ID集合（高效匹配方法）
    target_company_ids = set(company_df['lc_company_id'])
    
    # 释放不需要的内存
    del company_dfs, company_df
    gc.collect()
    
    # 2. 创建输出目录
    output_dir = "company_competitor_filtered"
    os.makedirs(output_dir, exist_ok=True)
    
    # 3. 处理竞品业务数据
    competitor_data_path = r"F:\蕾奥工作\20.数据库转到本地\output\t_product_competitor"
    competitor_files = [f for f in os.listdir(competitor_data_path) if f.endswith('.parquet')]
    competitor_files.sort()
    
    print(f"找到 {len(competitor_files)} 个竞品业务数据文件")
    
    batch_num = 1
    batch_records = []
    total_processed = 0
    
    for i, file in enumerate(competitor_files):
        print(f"处理文件 {i+1}/{len(competitor_files)}: {file}")
        
        file_path = os.path.join(competitor_data_path, file)
        
        try:
            # 读取文件，只保留需要的列
            df = pd.read_parquet(file_path, columns=['company_id', 'jingpin_yewu', 'is_deleted'])
            
            # 筛选未删除的记录
            df = df[df['is_deleted'] == 0].copy()
            
            if len(df) == 0:
                print(f"  文件 {file} 无有效记录，跳过")
                continue
            
            # 只保留需要的列
            df = df[['company_id', 'jingpin_yewu']]
            
            batch_records.append(df)
            total_processed += len(df)
            
            print(f"  添加 {len(df)} 条记录，累计 {total_processed} 条")
            
            # 当累计记录数超过100万时进行筛选和保存
            if total_processed >= 1000000:
                print(f"累计记录数达到 {total_processed}，开始筛选...")
                
                # 合并当前批次的数据
                batch_df = pd.concat(batch_records, ignore_index=True)
                
                # 使用集合交集进行高效筛选（比isin更快）
                batch_company_ids = set(batch_df['company_id'].unique())
                matched_company_ids = batch_company_ids & target_company_ids
                filtered_df = batch_df[batch_df['company_id'].isin(matched_company_ids)].copy()
                
                if len(filtered_df) > 0:
                    # 保存到文件
                    output_file = os.path.join(output_dir, f"company_competitor_batch_{batch_num:03d}.parquet")
                    filtered_df.to_parquet(output_file, index=False)
                    
                    print(f"  筛选出 {len(filtered_df)} 条记录，保存到 {output_file}")
                    batch_num += 1
                else:
                    print("  筛选后无匹配记录，跳过此批次")
                
                # 释放内存
                del batch_records, batch_df, filtered_df
                gc.collect()
                
                # 重置批次
                batch_records = []
                total_processed = 0
            
        except Exception as e:
            print(f"处理文件 {file} 时出错: {e}")
            continue
    
    # 处理剩余的记录
    if batch_records and total_processed > 0:
        print(f"处理剩余的 {total_processed} 条记录...")
        
        batch_df = pd.concat(batch_records, ignore_index=True)
        
        # 使用集合交集进行高效筛选
        batch_company_ids = set(batch_df['company_id'].unique())
        matched_company_ids = batch_company_ids & target_company_ids
        filtered_df = batch_df[batch_df['company_id'].isin(matched_company_ids)].copy()
        
        if len(filtered_df) > 0:
            output_file = os.path.join(output_dir, f"company_competitor_batch_{batch_num:03d}.parquet")
            filtered_df.to_parquet(output_file, index=False)
            
            print(f"筛选出 {len(filtered_df)} 条记录，保存到 {output_file}")
        else:
            print("剩余记录筛选后无匹配记录")
    
    print("企业竞品业务信息提取完成！")

if __name__ == "__main__":
    main()