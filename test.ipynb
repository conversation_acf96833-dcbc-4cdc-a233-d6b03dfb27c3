import pandas as pd

df = pd.read_parquet(r"result_date_part01\batch_001.parquet")

df

import os
import glob

def search_keyword_in_sql_files(folder_path, keyword="法人"):
    # 获取所有 .sql 文件路径
    sql_files = glob.glob(os.path.join(folder_path, "*.sql"))

    if not sql_files:
        print("未找到任何 .sql 文件。")
        return

    # 遍历每个文件
    for file_path in sql_files:
        try:
            with open(file_path, "r", encoding="utf-8") as file:
                for line_number, line in enumerate(file, start=1):
                    if keyword in line:
                        print(f"[{os.path.basename(file_path)}][Line {line_number}]: {line.strip()}")
        except UnicodeDecodeError:
            print(f"无法使用 utf-8 解码文件：{file_path}，请检查文件编码格式。")
        except Exception as e:
            print(f"读取文件出错：{file_path}，错误信息：{e}")

# 示例调用（替换为你实际的目录路径）
search_keyword_in_sql_files(r"F:\蕾奥工作\20.数据库转到本地\ddl")


import pandas as pd

df= pd.read_parquet(r"F:\蕾奥工作\11.自研挂链\输入数据整理\result_date_part01_update\batch_001.parquet")
df2= pd.read_parquet(r"F:\蕾奥工作\11.自研挂链\输入数据整理\result_date_part02\batch_001.parquet")

df.columns, df2.columns

df

b = df2[df2['yewu'].notna()]
b

b