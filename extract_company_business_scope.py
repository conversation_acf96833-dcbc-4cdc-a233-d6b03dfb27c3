"""
企业经营范围数据提取脚本
功能：从company_general_info_v3数据中提取企业基本信息和经营范围
"""

import pandas as pd
import os
from pathlib import Path
import gc

def extract_company_business_scope():
    """提取企业经营范围数据"""
    
    # 数据源路径
    source_dir = Path(r"F:\蕾奥工作\20.数据库转到本地\output\company_general_info_v3")
    
    # 输出路径
    output_base_dir = Path("company_business_scope_output")
    basic_info_dir = output_base_dir / "company_basic_info"
    business_scope_dir = output_base_dir / "company_business_scope"
    
    # 创建输出目录
    basic_info_dir.mkdir(parents=True, exist_ok=True)
    business_scope_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取所有parquet文件
    parquet_files = list(source_dir.glob("*.parquet"))
    print(f"找到 {len(parquet_files)} 个parquet文件")
    
    # 初始化变量
    batch_data = []
    batch_count = 0
    total_processed = 0
    batch_size_limit = 1000000  # 100万条记录
    
    # 需要的列
    required_columns = ['lc_company_id', 'company_name', 'business_scope', 'company_status_clean', 'industry_l1_code']
    
    for file_path in parquet_files:
        print(f"正在处理文件: {file_path.name}")
        
        try:
            # 读取parquet文件
            df = pd.read_parquet(file_path, columns=required_columns)
            
            # 筛选条件：company_status_clean in [1, 5, 6]
            filtered_df = df[df['company_status_clean'].isin([1, 5, 6])].copy()
            
            if len(filtered_df) > 0:
                batch_data.append(filtered_df)
                total_processed += len(filtered_df)
                print(f"  - 筛选后记录数: {len(filtered_df)}, 累计: {total_processed}")
                
                # 检查是否达到批次大小限制
                if total_processed >= batch_size_limit:
                    batch_count += 1
                    save_batch_data(batch_data, batch_count, basic_info_dir, business_scope_dir)
                    
                    # 清理内存
                    del batch_data, filtered_df
                    gc.collect()
                    
                    # 重置变量
                    batch_data = []
                    total_processed = 0
                    print(f"批次 {batch_count} 已保存并清理内存")
            
            # 清理当前文件数据
            del df
            if 'filtered_df' in locals():
                del filtered_df
            gc.collect()
            
        except Exception as e:
            print(f"处理文件 {file_path.name} 时出错: {e}")
            continue
    
    # 保存剩余数据
    if batch_data and total_processed > 0:
        batch_count += 1
        save_batch_data(batch_data, batch_count, basic_info_dir, business_scope_dir)
        print(f"最后批次 {batch_count} 已保存")
    
    print(f"数据提取完成，共处理 {batch_count} 个批次")

def save_batch_data(batch_data_list, batch_num, basic_info_dir, business_scope_dir):
    """保存批次数据到两个不同的文件夹"""
    
    # 合并所有批次数据
    combined_df = pd.concat(batch_data_list, ignore_index=True)
    
    # 保存基本信息 (lc_company_id, company_name, company_status_clean)
    basic_info_df = combined_df[['lc_company_id', 'company_name', 'company_status_clean', 'industry_l1_code']].copy()
    basic_info_file = basic_info_dir / f"company_basic_info_batch_{batch_num:03d}.parquet"
    basic_info_df.to_parquet(basic_info_file, index=False)
    
    # 保存经营范围 (lc_company_id, business_scope)
    business_scope_df = combined_df[['lc_company_id', 'business_scope']].copy()
    business_scope_file = business_scope_dir / f"company_business_scope_batch_{batch_num:03d}.parquet"
    business_scope_df.to_parquet(business_scope_file, index=False)
    
    print(f"  批次 {batch_num} 保存完成:")
    print(f"    基本信息: {basic_info_file} ({len(basic_info_df)} 条记录)")
    print(f"    经营范围: {business_scope_file} ({len(business_scope_df)} 条记录)")
    
    # 清理临时数据
    del combined_df, basic_info_df, business_scope_df

if __name__ == "__main__":
    print("开始提取企业经营范围数据...")
    extract_company_business_scope()
    print("提取完成！")