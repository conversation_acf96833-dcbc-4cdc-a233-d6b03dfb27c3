"""
企业专利信息提取脚本
功能：从t_patent_info数据中提取指定企业的专利信息
"""

import pandas as pd
import os
from pathlib import Path
import gc

def extract_company_patent_info():
    """提取企业专利信息"""
    
    # 数据源路径
    basic_info_dir = Path("company_business_scope_output/company_basic_info")
    patent_source_dir = Path(r"F:\蕾奥工作\20.数据库转到本地\output\t_patent_info")
    
    # 输出路径
    output_dir = Path("company_patent_filtered")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 步骤1：读取并合并所有基本信息文件
    print("正在读取企业基本信息...")
    basic_info_files = list(basic_info_dir.glob("*.parquet"))
    print(f"找到 {len(basic_info_files)} 个基本信息文件")
    
    # 合并所有基本信息文件，需要company_name和lc_company_id
    company_info_list = []
    for file_path in basic_info_files:
        df = pd.read_parquet(file_path, columns=['lc_company_id', 'company_name'])
        company_info_list.append(df)
    
    # 合并并去重
    target_companies_df = pd.concat(company_info_list, ignore_index=True).drop_duplicates()
    
    # 创建company_name到lc_company_id的映射字典（更高效的匹配方法）
    company_name_to_id = dict(zip(target_companies_df['company_name'], target_companies_df['lc_company_id']))
    target_company_names_set = set(target_companies_df['company_name'])
    
    print(f"目标企业数量: {len(target_company_names_set):,}")
    
    # 清理临时数据
    del company_info_list, target_companies_df
    gc.collect()
    
    # 步骤2：处理专利信息文件
    patent_files = list(patent_source_dir.glob("*.parquet"))
    print(f"找到 {len(patent_files)} 个专利信息文件")
    
    # 初始化变量
    batch_data = []
    batch_count = 0
    total_processed = 0
    batch_size_limit = 1000000  # 100万条记录
    
    # 需要的列
    required_columns = ['applicant_name', 'abs', 'title', 'is_deleted', 'status']
    
    for file_path in patent_files:
        print(f"正在处理文件: {file_path.name}")
        
        try:
            # 读取专利信息文件，只保留需要的列
            df = pd.read_parquet(file_path, columns=required_columns)
            
            # 筛选is_deleted=0且status='授权'的记录
            df = df[(df['is_deleted'] == 0) & (df['status'] == '授权')].copy()
            
            # 删除筛选列，重命名字段
            df = df.drop(columns=['is_deleted', 'status'])
            df = df.rename(columns={'abs': 'patent_abs', 'title': 'patent_title'})
            
            if len(df) > 0:
                batch_data.append(df)
                total_processed += len(df)
                print(f"  - 当前文件有效记录数: {len(df)}, 累计: {total_processed}")
                
                # 检查是否达到批次大小限制
                if total_processed >= batch_size_limit:
                    batch_count += 1
                    process_and_save_patent_batch(batch_data, batch_count, target_company_names_set, 
                                                company_name_to_id, output_dir)
                    
                    # 清理内存
                    del batch_data
                    gc.collect()
                    
                    # 重置变量
                    batch_data = []
                    total_processed = 0
                    print(f"批次 {batch_count} 处理完成并清理内存")
            else:
                print(f"  - 当前文件筛选后无有效记录")
            
            # 清理当前文件数据
            del df
            gc.collect()
            
        except Exception as e:
            print(f"处理文件 {file_path.name} 时出错: {e}")
            continue
    
    # 处理剩余数据
    if batch_data and total_processed > 0:
        batch_count += 1
        process_and_save_patent_batch(batch_data, batch_count, target_company_names_set, 
                                    company_name_to_id, output_dir)
        print(f"最后批次 {batch_count} 处理完成")
    
    print(f"企业专利信息提取完成，共处理 {batch_count} 个批次")

def process_and_save_patent_batch(batch_data_list, batch_num, target_company_names_set, 
                                company_name_to_id, output_dir):
    """处理并保存批次数据"""
    
    # 合并批次数据
    combined_df = pd.concat(batch_data_list, ignore_index=True)
    print(f"  批次 {batch_num} 合并后记录数: {len(combined_df)}")
    
    # 使用集合交集进行高效筛选（比isin更快）
    batch_applicants = set(combined_df['applicant_name'].unique())
    matched_applicants = batch_applicants & target_company_names_set
    filtered_df = combined_df[combined_df['applicant_name'].isin(matched_applicants)].copy()
    
    if len(filtered_df) > 0:
        # 使用字典映射添加lc_company_id（比merge更高效）
        filtered_df['lc_company_id'] = filtered_df['applicant_name'].map(company_name_to_id)
        
        # 只保留需要的列：lc_company_id, patent_abs, patent_title
        result_df = filtered_df[['lc_company_id', 'patent_abs', 'patent_title']].copy()
        
        # 保存筛选后的数据
        output_file = output_dir / f"company_patent_batch_{batch_num:03d}.parquet"
        result_df.to_parquet(output_file, index=False)
        print(f"  批次 {batch_num} 保存完成: {output_file} ({len(result_df)} 条记录)")
        
        # 清理结果数据
        del result_df
    else:
        print(f"  批次 {batch_num} 筛选后无记录，跳过保存")
    
    # 清理临时数据
    del combined_df, filtered_df
    gc.collect()

if __name__ == "__main__":
    print("开始提取企业专利信息...")
    extract_company_patent_info()
    print("提取完成！")