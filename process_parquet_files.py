#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Parquet文件数据处理脚本
处理result_date_part01文件夹下的所有.parquet文件，并输出到result_date_part01_update文件夹
"""

import os
import re
import pandas as pd
from tqdm import tqdm
import glob
from pathlib import Path


def convert_punctuation_and_case(value):
    """
    转换标点符号和字母大小写
    - 半角分号转全角分号
    - 半角括号转全角括号
    - 英文字母转小写
    支持字符串和列表类型
    """
    try:
        if pd.isna(value):
            return value
        if value == '' or value is None:
            return value

        # 如果是列表或数组，处理每个元素
        if isinstance(value, (list, tuple)) or hasattr(value, '__iter__') and not isinstance(value, str):
            try:
                result = []
                for item in value:
                    if pd.isna(item) or item == '' or item is None:
                        continue
                    item_str = str(item)
                    # 半角转全角
                    item_str = item_str.replace(';', '；')
                    item_str = item_str.replace('(', '（')
                    item_str = item_str.replace(')', '）')
                    # 英文字母转小写
                    item_str = item_str.lower()
                    result.append(item_str)
                return result
            except:
                # 如果迭代失败，当作字符串处理
                pass

        # 字符串处理
        text = str(value)
        # 半角转全角
        text = text.replace(';', '；')
        text = text.replace('(', '（')
        text = text.replace(')', '）')

        # 英文字母转小写
        text = text.lower()

        return text
    except Exception:
        return value


def remove_exclusion_statements(value):
    """
    删除括号包裹的排除性语句
    包括：（不含……）、（不包含……）、（除……）、（不包括……）、（不得……）
    支持字符串和列表类型
    """
    try:
        if pd.isna(value):
            return value
        if value == '' or value is None:
            return value

        # 定义排除性语句的正则模式 - 更精确的匹配
        exclusion_patterns = [
            r'（不含[^）]*?）',
            r'（不包含[^）]*?）',
            r'（除[^）]*?）',
            r'（不包括[^）]*?）',
            r'（不得[^）]*?）'
        ]

        # 如果是列表或数组，处理每个元素
        if isinstance(value, (list, tuple)) or hasattr(value, '__iter__') and not isinstance(value, str):
            try:
                result = []
                for item in value:
                    if pd.isna(item) or item == '' or item is None:
                        continue
                    item_str = str(item)
                    # 删除所有匹配的排除性语句
                    for pattern in exclusion_patterns:
                        item_str = re.sub(pattern, '', item_str)
                    item_str = item_str.strip()
                    if item_str:  # 只添加非空字符串
                        result.append(item_str)
                return result
            except:
                # 如果迭代失败，当作字符串处理
                pass

        # 字符串处理
        text = str(value)
        # 删除所有匹配的排除性语句
        for pattern in exclusion_patterns:
            text = re.sub(pattern, '', text)

        return text.strip()
    except Exception:
        return value


def split_business_scope(text):
    """
    将business_scope字段按全角分号分割成列表
    如果不包含全角分号，返回包含原值的列表
    """
    try:
        if pd.isna(text):
            return []
        if text == '' or text is None:
            return []

        text = str(text)

        # 按全角分号分割
        if '；' in text:
            items = text.split('；')
        else:
            items = [text]

        # 移除空字符串
        items = [item.strip() for item in items if item.strip()]

        return items
    except Exception:
        return []


def clean_list_values(value):
    """
    清理列表值，移除空字符串元素
    """
    try:
        if pd.isna(value):
            return []
        if value == '' or value is None:
            return []

        if isinstance(value, list):
            return [item for item in value if item and str(item).strip()]
        else:
            return [value] if value and str(value).strip() else []
    except Exception:
        return []


def process_dataframe(df):
    """
    处理DataFrame中的数据
    """
    try:
        # 获取需要处理的列（除了lc_company_id、company_name和company_status_clean）
        excluded_columns = ['lc_company_id', 'company_name', 'company_status_clean']
        columns_to_process = [col for col in df.columns if col not in excluded_columns]

        print(f"  处理字段: {columns_to_process}")

        # 创建副本以避免修改原数据
        df_processed = df.copy()

        # 1. 对所有需要处理的字段进行标点符号和字母转换
        for col in columns_to_process:
            if col in df_processed.columns:
                print(f"    处理标点符号和大小写: {col}")
                df_processed[col] = df_processed[col].apply(convert_punctuation_and_case)

        # 2. 对business_scope和allow_content字段删除排除性语句
        special_fields = ['business_scope', 'allow_content']
        for col in special_fields:
            if col in df_processed.columns:
                print(f"    删除排除性语句: {col}")
                df_processed[col] = df_processed[col].apply(remove_exclusion_statements)

        # 3. 对business_scope字段进行分割
        if 'business_scope' in df_processed.columns:
            print(f"    分割business_scope字段")
            df_processed['business_scope'] = df_processed['business_scope'].apply(split_business_scope)

        # 4. 清理business_scope列表值（只对business_scope字段，因为其他字段不需要转换为列表）
        # 注意：business_scope在步骤3中已经转换为列表，这里不需要再次清理

        return df_processed

    except Exception as e:
        print(f"    处理DataFrame时出错: {str(e)}")
        return df


def main():
    """
    主函数
    """
    # 输入和输出文件夹路径
    input_folder = "result_date_part01"
    output_folder = "result_date_part01_update"
    
    # 检查输入文件夹是否存在
    if not os.path.exists(input_folder):
        print(f"错误：输入文件夹 '{input_folder}' 不存在！")
        return
    
    # 创建输出文件夹
    os.makedirs(output_folder, exist_ok=True)
    print(f"输出文件夹已创建: {output_folder}")
    
    # 获取所有parquet文件
    parquet_files = glob.glob(os.path.join(input_folder, "*.parquet"))
    
    if not parquet_files:
        print(f"在 '{input_folder}' 文件夹中未找到任何 .parquet 文件！")
        return
    
    print(f"找到 {len(parquet_files)} 个 .parquet 文件")
    
    # 处理每个文件
    for file_path in tqdm(parquet_files, desc="处理文件"):
        try:
            # 获取文件名
            file_name = os.path.basename(file_path)
            output_path = os.path.join(output_folder, file_name)
            
            print(f"\n正在处理: {file_name}")
            
            # 读取parquet文件
            df = pd.read_parquet(file_path)
            print(f"  原始数据形状: {df.shape}")
            
            # 处理数据
            df_processed = process_dataframe(df)
            print(f"  处理后数据形状: {df_processed.shape}")
            
            # 保存处理后的文件
            df_processed.to_parquet(output_path, index=False)
            print(f"  已保存到: {output_path}")
            
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
            continue
    
    print(f"\n所有文件处理完成！结果保存在 '{output_folder}' 文件夹中。")


if __name__ == "__main__":
    main()
