# 企业数据批量处理脚本说明

## 概述
本目录包含多个数据提取脚本，用于从各种数据源中筛选和提取目标企业的相关信息。所有脚本都基于统一的企业基本信息进行筛选，确保数据的一致性和完整性。

## 目标企业定义
所有脚本都基于 `company_business_scope_output/company_basic_info` 目录下的企业基本信息作为筛选标准：
- **筛选条件**: `company_status_clean` 为 1、5、6 的企业
- **关键字段**: `lc_company_id`（主要关联字段）、`company_name`（辅助匹配字段）

## 脚本详细说明

### 1. extract_company_business_scope.py
**功能**: 提取企业基本信息和经营范围
- **数据源**: `company_general_info_v3`
- **筛选条件**: `company_status_clean` 为 1、5、6
- **输出目录**: 
  - `company_business_scope_output/company_basic_info` (基本信息)
  - `company_business_scope_output/company_business_scope` (经营范围)
- **输出字段**:
  - 基本信息: `lc_company_id`, `company_name`, `company_status_clean`
  - 经营范围: `lc_company_id`, `business_scope`

### 2. extract_bid_winners.py
**功能**: 提取招投标中标单位信息
- **数据源**: `filtered_bid_results` 目录下的筛选后招投标数据
- **处理逻辑**: 
  - 拆分 `bid_win` 字段（按逗号分隔）
  - 每个中标单位生成独立记录
- **输出目录**: `extracted_bid_winners`
- **输出字段**: 拆分后的中标单位信息

### 3. extract_company_allow_info.py
**功能**: 提取企业行政许可信息
- **数据源**: `t_company_allow`
- **筛选条件**: 基于 `company_id` 匹配目标企业
- **输出目录**: `company_allow_filtered`
- **输出字段**: `company_id`, `allow_content`

### 4. extract_company_app_info.py
**功能**: 提取企业APP信息
- **数据源**: `t_app_info`
- **筛选条件**: 基于 `company_id` 匹配目标企业
- **字段重命名**: `brief` → `app_brief`
- **输出目录**: `company_app_filtered`

### 5. extract_company_competitor_info.py
**功能**: 提取企业竞品业务信息
- **数据源**: `t_product_competitor`
- **筛选条件**: 
  - `is_deleted` = 0
  - 基于 `company_id` 匹配目标企业
- **输出目录**: `company_competitor_filtered`

### 6. extract_company_investment_info.py
**功能**: 提取企业投资事件信息
- **数据源**: `t_investment_event`
- **筛选条件**: 
  - `is_deleted` = 0
  - 基于 `company_id` 匹配目标企业
- **字段重命名**: `introduce` → `product_intro`
- **输出目录**: `company_investment_filtered`

### 7. extract_company_land_info.py
**功能**: 提取企业购地信息
- **数据源**: `t_land_announcement`
- **筛选条件**: 
  - `is_deleted` = 0
  - 基于 `company_id` 匹配目标企业
- **字段重命名**: `project_name` → `land_project_name`
- **输出目录**: `company_land_filtered`

### 8. extract_company_patent_info.py
**功能**: 提取企业专利信息
- **数据源**: `t_patent_info`
- **筛选条件**: 
  - `is_deleted` = 0
  - `status` = "授权"
  - 基于 `applicant_name` 匹配目标企业名称
- **字段重命名**: 
  - `abs` → `patent_abs`
  - `title` → `patent_title`
- **输出目录**: `company_patent_filtered`
- **特殊说明**: 使用企业名称进行匹配，而非ID

### 9. extract_company_profiles.py
**功能**: 提取企业简介信息
- **数据源**: `t_company_profile`
- **筛选条件**: 基于 `company_id` 或 `lc_company_id` 匹配目标企业
- **输出目录**: `company_profile_filtered`

### 10. extract_company_software_info.py
**功能**: 提取企业软件著作权信息
- **数据源**: `t_copyright_software`
- **筛选条件**: 
  - `is_deleted` = 0
  - 基于 `author` 字段匹配目标企业名称
- **字段重命名**: `full_name` → `software_full_name`
- **输出目录**: `company_software_filtered`
- **特殊说明**: 使用企业名称进行匹配，而非ID

### 11. extract_main_business_info.py
**功能**: 提取企业业务信息
- **数据源**: `t_main_business`
- **筛选条件**: 
  - `is_deleted` = 0
  - 基于 `company_id` 匹配目标企业
- **输出目录**: `main_business_filtered`
- **输出字段**: `company_id`, `yewu`, `portray`

### 12. extract_stock_company_info.py
**功能**: 提取上市企业信息
- **数据源**: `t_stock_base`
- **筛选条件**: 基于 `company_id` 匹配目标企业
- **字段重命名**: `business_scope` → `stock_business_scope`
- **输出目录**: `stock_company_filtered`
- **输出字段**: `company_id`, `introduction`, `main_product`, `stock_business_scope`

## 通用处理规则

### 数据筛选规则
1. **企业状态筛选**: 只处理 `company_status_clean` 为 1、5、6 的企业
2. **删除状态筛选**: 大部分数据源都筛选 `is_deleted` = 0 的记录
3. **专利状态筛选**: 专利数据额外筛选 `status` = "授权"

### 匹配方式
1. **ID匹配**: 大部分脚本使用 `company_id` 或 `lc_company_id` 进行匹配
2. **名称匹配**: 专利和软件著作权使用企业名称进行匹配
   - 专利: `applicant_name` 匹配 `company_name`
   - 软件著作权: `author` 匹配 `company_name`

### 性能优化策略
1. **批量处理**: 所有脚本都采用批量处理机制，避免内存溢出
2. **内存管理**: 使用 `gc.collect()` 主动释放内存
3. **高效筛选**: 使用集合交集和字典映射提高筛选效率
4. **分批保存**: 达到一定记录数后分批保存，避免单文件过大

### 输出格式
- **文件格式**: 统一使用 Parquet 格式
- **命名规则**: `{数据类型}_batch_{批次号:03d}.parquet`
- **批次大小**: 通常以100万条记录为一个批次

## 数据源路径说明
- **本地路径**: `company_business_scope_output/company_basic_info`
- **H盘路径**: `H:\数据库\{表名}`
- **F盘路径**: `F:\蕾奥工作\20.数据库转到本地\output\{表名}`

## 注意事项
1. 确保所有数据源路径存在且可访问
2. 运行前确保有足够的磁盘空间存储输出文件
3. 建议按顺序执行，先运行 `extract_company_business_scope.py` 生成基础企业信息
4. 监控内存使用情况，必要时调整批次大小
5. 定期检查输出文件的完整性和正确性

## 验证建议
1. 检查输出目录是否包含预期的批次文件
2. 验证输出文件的记录数是否合理
3. 抽样检查数据内容的正确性
4. 确认字段重命名是否正确执行
5. 验证筛选条件是否正确应用