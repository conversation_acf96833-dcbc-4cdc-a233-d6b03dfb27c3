#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理招投标数据脚本
功能：从大量parquet文件中筛选特定字段和条件的数据，分批保存以控制内存使用
"""

import os
import gc
import pandas as pd
from pathlib import Path
from typing import List
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BidDataProcessor:
    """招投标数据处理器"""
    
    def __init__(self, source_dir: str, output_dir: str, batch_size: int = 100):
        """
        初始化处理器
        
        Args:
            source_dir: 源数据目录
            output_dir: 输出目录
            batch_size: 每批处理的文件数量
        """
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.batch_size = batch_size
        self.target_columns = ['title', 'purchaser', 'bid_win', 'notice_type_sub']
        self.valid_notice_types = ['成交', '中标', '合同及验收']
        
        # 创建输出目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def get_parquet_files(self) -> List[Path]:
        """获取所有parquet文件路径"""
        parquet_files = list(self.source_dir.glob("*.parquet"))
        logger.info(f"找到 {len(parquet_files)} 个parquet文件")
        return parquet_files
    
    def process_single_file(self, file_path: Path) -> pd.DataFrame:
        """
        处理单个parquet文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            处理后的DataFrame
        """
        try:
            # 只读取需要的列
            df = pd.read_parquet(file_path, columns=self.target_columns)
            
            # 筛选符合条件的记录
            filtered_df = df[df['notice_type_sub'].isin(self.valid_notice_types)]
            
            logger.info(f"文件 {file_path.name}: 原始记录 {len(df)}, 筛选后 {len(filtered_df)}")
            
            return filtered_df
            
        except Exception as e:
            logger.error(f"处理文件 {file_path.name} 时出错: {e}")
            return pd.DataFrame()
    
    def save_batch(self, batch_data: pd.DataFrame, batch_num: int):
        """
        保存批次数据
        
        Args:
            batch_data: 批次数据
            batch_num: 批次编号
        """
        if len(batch_data) > 0:
            output_file = self.output_dir / f"filtered_bid_data_batch_{batch_num:03d}.parquet"
            batch_data.to_parquet(output_file, index=False)
            logger.info(f"批次 {batch_num} 已保存: {len(batch_data)} 条记录 -> {output_file.name}")
        else:
            logger.warning(f"批次 {batch_num} 无数据，跳过保存")
    
    def process_all_files(self):
        """处理所有文件的主流程"""
        parquet_files = self.get_parquet_files()
        
        if not parquet_files:
            logger.warning("未找到任何parquet文件")
            return
        
        batch_num = 1
        batch_data_list = []
        total_processed = 0
        
        for i, file_path in enumerate(parquet_files, 1):
            logger.info(f"正在处理第 {i}/{len(parquet_files)} 个文件: {file_path.name}")
            
            # 处理单个文件
            file_data = self.process_single_file(file_path)
            
            if len(file_data) > 0:
                batch_data_list.append(file_data)
            
            # 每处理batch_size个文件或处理完所有文件时保存一批
            if i % self.batch_size == 0 or i == len(parquet_files):
                if batch_data_list:
                    # 合并当前批次的所有数据
                    batch_data = pd.concat(batch_data_list, ignore_index=True)
                    
                    # 保存批次数据
                    self.save_batch(batch_data, batch_num)
                    total_processed += len(batch_data)
                    
                    # 清理内存
                    del batch_data
                    del batch_data_list
                    gc.collect()
                    
                    # 重置批次数据列表
                    batch_data_list = []
                    batch_num += 1
                    
                    logger.info(f"内存已释放，累计处理 {total_processed} 条记录")
        
        logger.info(f"处理完成！总共处理 {total_processed} 条有效记录，保存为 {batch_num-1} 个批次文件")

def main():
    """主函数"""
    # 配置路径
    source_directory = r"F:\蕾奥工作\20.数据库转到本地\output\t_company_bid_main"
    output_directory = r"F:\蕾奥工作\11.自研挂链\输入数据整理\filtered_bid_results"
    
    # 创建处理器并执行
    processor = BidDataProcessor(
        source_dir=source_directory,
        output_dir=output_directory,
        batch_size=100  # 每100个文件保存一次
    )
    
    processor.process_all_files()

if __name__ == "__main__":
    main()