"""
企业软件著作权信息提取脚本
从t_copyright_software数据中提取目标企业的软件著作权信息
"""

import pandas as pd
import os
import gc
from pathlib import Path

def main():
    # 1. 读取目标企业基本信息
    print("正在读取企业基本信息...")
    company_basic_path = "company_business_scope_output/company_basic_info"
    company_dfs = []
    
    for file in os.listdir(company_basic_path):
        if file.endswith('.parquet'):
            df = pd.read_parquet(os.path.join(company_basic_path, file))
            company_dfs.append(df[['lc_company_id', 'company_name']])
    
    company_df = pd.concat(company_dfs, ignore_index=True)
    print(f"读取到 {len(company_df)} 条企业基本信息")
    
    # 创建公司名称到ID的映射字典（高效匹配方法）
    company_name_to_id = dict(zip(company_df['company_name'], company_df['lc_company_id']))
    target_company_names = set(company_df['company_name'])
    
    # 释放不需要的内存
    del company_dfs, company_df
    gc.collect()
    
    # 2. 创建输出目录
    output_dir = "company_software_filtered"
    os.makedirs(output_dir, exist_ok=True)
    
    # 3. 处理软件著作权数据
    software_data_path = r"H:\数据库\t_copyright_software"
    software_files = [f for f in os.listdir(software_data_path) if f.endswith('.parquet')]
    software_files.sort()
    
    print(f"找到 {len(software_files)} 个软件著作权数据文件")
    
    batch_num = 1
    batch_records = []
    total_processed = 0
    
    for i, file in enumerate(software_files):
        print(f"处理文件 {i+1}/{len(software_files)}: {file}")
        
        file_path = os.path.join(software_data_path, file)
        
        try:
            # 读取文件，只保留需要的列
            df = pd.read_parquet(file_path, columns=['author', 'full_name', 'is_deleted'])
            
            # 筛选未删除的记录
            df = df[df['is_deleted'] == 0].copy()
            
            if len(df) == 0:
                print(f"  文件 {file} 无有效记录，跳过")
                continue
            
            # 重命名列
            df = df.rename(columns={'full_name': 'software_full_name'})
            df = df[['author', 'software_full_name']]
            
            batch_records.append(df)
            total_processed += len(df)
            
            print(f"  添加 {len(df)} 条记录，累计 {total_processed} 条")
            
            # 当累计记录数超过100万时进行筛选和保存
            if total_processed >= 1000000:
                print(f"累计记录数达到 {total_processed}，开始筛选...")
                
                # 合并当前批次的数据
                batch_df = pd.concat(batch_records, ignore_index=True)
                
                # 使用集合交集进行高效筛选（比isin更快）
                batch_authors = set(batch_df['author'].unique())
                matched_authors = batch_authors & target_company_names
                filtered_df = batch_df[batch_df['author'].isin(matched_authors)].copy()
                
                if len(filtered_df) > 0:
                    # 使用字典映射添加lc_company_id（比merge更高效）
                    filtered_df['lc_company_id'] = filtered_df['author'].map(company_name_to_id)
                    
                    # 只保留需要的列
                    result_df = filtered_df[['lc_company_id', 'software_full_name']].copy()
                    
                    # 保存到文件
                    output_file = os.path.join(output_dir, f"company_software_batch_{batch_num:03d}.parquet")
                    result_df.to_parquet(output_file, index=False)
                    
                    print(f"  筛选出 {len(result_df)} 条记录，保存到 {output_file}")
                    batch_num += 1
                else:
                    print("  筛选后无匹配记录，跳过此批次")
                
                # 释放内存
                del batch_records, batch_df, filtered_df
                if 'result_df' in locals():
                    del result_df
                gc.collect()
                
                # 重置批次
                batch_records = []
                total_processed = 0
            
        except Exception as e:
            print(f"处理文件 {file} 时出错: {e}")
            continue
    
    # 处理剩余的记录
    if batch_records and total_processed > 0:
        print(f"处理剩余的 {total_processed} 条记录...")
        
        batch_df = pd.concat(batch_records, ignore_index=True)
        
        # 使用集合交集进行高效筛选
        batch_authors = set(batch_df['author'].unique())
        matched_authors = batch_authors & target_company_names
        filtered_df = batch_df[batch_df['author'].isin(matched_authors)].copy()
        
        if len(filtered_df) > 0:
            filtered_df['lc_company_id'] = filtered_df['author'].map(company_name_to_id)
            result_df = filtered_df[['lc_company_id', 'software_full_name']].copy()
            
            output_file = os.path.join(output_dir, f"company_software_batch_{batch_num:03d}.parquet")
            result_df.to_parquet(output_file, index=False)
            
            print(f"筛选出 {len(result_df)} 条记录，保存到 {output_file}")
        else:
            print("剩余记录筛选后无匹配记录")
    
    print("软件著作权信息提取完成！")

if __name__ == "__main__":
    main()