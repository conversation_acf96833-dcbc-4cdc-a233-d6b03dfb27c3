#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中标单位提取脚本
功能：从筛选后的招投标数据中提取中标单位信息，拆分多个中标单位并规范化存储
"""

import os
import pandas as pd
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BidWinnerExtractor:
    """中标单位提取器"""
    
    def __init__(self, source_dir: str, output_dir: str):
        """
        初始化提取器
        
        Args:
            source_dir: 源数据目录（筛选后的parquet文件）
            output_dir: 输出目录
        """
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        
        # 创建输出目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def get_batch_files(self):
        """获取所有批次文件"""
        batch_files = list(self.source_dir.glob("filtered_bid_data_batch_*.parquet"))
        batch_files.sort()  # 按文件名排序
        logger.info(f"找到 {len(batch_files)} 个批次文件")
        return batch_files
    
    def extract_bid_winners(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        提取并拆分中标单位
        
        Args:
            df: 输入DataFrame
            
        Returns:
            处理后的DataFrame，包含title和bid_win字段
        """
        # 筛选bid_win非空的记录
        valid_records = df[df['bid_win'].notna() & (df['bid_win'].str.strip() != '')].copy()
        
        if len(valid_records) == 0:
            return pd.DataFrame(columns=['title', 'bid_win'])
        
        # 只保留需要的字段
        result_data = []
        
        for _, row in valid_records.iterrows():
            title = row['title']
            bid_win = str(row['bid_win']).strip()
            
            # 按逗号拆分中标单位
            winners = [winner.strip() for winner in bid_win.split(',') if winner.strip()]
            
            # 为每个中标单位创建一条记录
            for winner in winners:
                result_data.append({
                    'title': title,
                    'bid_win': winner
                })
        
        return pd.DataFrame(result_data)
    
    def process_single_batch(self, batch_file: Path) -> pd.DataFrame:
        """
        处理单个批次文件
        
        Args:
            batch_file: 批次文件路径
            
        Returns:
            处理后的DataFrame
        """
        try:
            logger.info(f"正在处理: {batch_file.name}")
            
            # 读取批次文件
            df = pd.read_parquet(batch_file)
            logger.info(f"读取记录数: {len(df)}")
            
            # 提取中标单位
            result_df = self.extract_bid_winners(df)
            logger.info(f"提取中标记录数: {len(result_df)}")
            
            return result_df
            
        except Exception as e:
            logger.error(f"处理文件 {batch_file.name} 时出错: {e}")
            return pd.DataFrame(columns=['title', 'bid_win'])
    
    def save_extracted_data(self, data: pd.DataFrame, batch_num: int):
        """
        保存提取的数据
        
        Args:
            data: 提取的数据
            batch_num: 批次编号
        """
        if len(data) > 0:
            output_file = self.output_dir / f"bid_winners_batch_{batch_num:03d}.parquet"
            data.to_parquet(output_file, index=False)
            logger.info(f"批次 {batch_num} 已保存: {len(data)} 条记录 -> {output_file.name}")
        else:
            logger.warning(f"批次 {batch_num} 无有效数据，跳过保存")
    
    def process_all_batches(self):
        """处理所有批次文件的主流程"""
        batch_files = self.get_batch_files()
        
        if not batch_files:
            logger.warning("未找到任何批次文件")
            return
        
        total_extracted = 0
        
        for i, batch_file in enumerate(batch_files, 1):
            # 处理单个批次
            extracted_data = self.process_single_batch(batch_file)
            
            # 保存提取的数据
            if len(extracted_data) > 0:
                self.save_extracted_data(extracted_data, i)
                total_extracted += len(extracted_data)
            
            logger.info(f"批次 {i}/{len(batch_files)} 处理完成，累计提取 {total_extracted} 条中标记录")
        
        logger.info(f"全部处理完成！总共提取 {total_extracted} 条中标单位记录")
        
        # 生成汇总统计
        self.generate_summary(total_extracted, len(batch_files))
    
    def generate_summary(self, total_records: int, total_batches: int):
        """生成处理汇总信息"""
        summary_file = self.output_dir / "extraction_summary.txt"
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("中标单位提取汇总报告\n")
            f.write("=" * 30 + "\n")
            f.write(f"处理批次数: {total_batches}\n")
            f.write(f"提取记录数: {total_records:,}\n")
            f.write(f"输出文件数: {total_batches}\n")
            f.write(f"输出目录: {self.output_dir}\n")
            f.write("\n说明:\n")
            f.write("- 每条记录包含项目标题(title)和单个中标单位(bid_win)\n")
            f.write("- 原始记录中包含多个中标单位的已拆分为多条记录\n")
            f.write("- 只保留bid_win字段非空的有效记录\n")
        
        logger.info(f"汇总报告已保存: {summary_file}")

def main():
    """主函数"""
    # 配置路径
    source_directory = r"f:\蕾奥工作\11.自研挂链\输入数据整理\filtered_bid_results"
    output_directory = r"f:\蕾奥工作\11.自研挂链\输入数据整理\extracted_bid_winners"
    
    # 创建提取器并执行
    extractor = BidWinnerExtractor(
        source_dir=source_directory,
        output_dir=output_directory
    )
    
    extractor.process_all_batches()

if __name__ == "__main__":
    main()