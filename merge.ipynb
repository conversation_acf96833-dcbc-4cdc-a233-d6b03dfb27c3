{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1ac622aa", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os"]}, {"cell_type": "code", "execution_count": 2, "id": "27f51273", "metadata": {}, "outputs": [], "source": ["# 读取basic_info和business_scope文件夹下的所有parquet文件，并且分别合并成单独的两个df\n", "# 定义文件夹路径\n", "basic_info_path = 'company_basic_info'\n", "business_scope_path = 'company_business_scope'\n", "\n", "# 读取并合并basic_info文件夹下的所有parquet文件\n", "basic_info_files = [f for f in os.listdir(basic_info_path) if f.endswith('.parquet')]\n", "basic_info_df = pd.concat([pd.read_parquet(os.path.join(basic_info_path, f)) for f in basic_info_files], ignore_index=True)\n", "\n", "# 读取并合并business_scope文件夹下的所有parquet文件\n", "business_scope_files = [f for f in os.listdir(business_scope_path) if f.endswith('.parquet')]\n", "business_scope_df = pd.concat([pd.read_parquet(os.path.join(business_scope_path, f)) for f in business_scope_files], ignore_index=True)\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "6fa7512a", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(basic_info_df, business_scope_df, on='lc_company_id', how='left')"]}, {"cell_type": "code", "execution_count": 6, "id": "cb741c1d", "metadata": {}, "outputs": [], "source": ["del basic_info_df\n", "del business_scope_df"]}, {"cell_type": "code", "execution_count": 7, "id": "bcc258b0", "metadata": {}, "outputs": [], "source": ["# 读取company_allow_filtered文件夹下的所有parquet文件，然后合并成一个df\n", "allow_files = [f for f in os.listdir('company_allow_filtered') if f.endswith('.parquet')]\n", "allow_df = pd.concat([pd.read_parquet(os.path.join('company_allow_filtered', f)) for f in allow_files], ignore_index=True)"]}, {"cell_type": "code", "execution_count": 10, "id": "09e07d79", "metadata": {}, "outputs": [], "source": ["# 清除allow_content列为None或者空字符串的行，然后根据company_id进行分组合并，对于同一个company_id的allow_content以列表形式组合\n", "allow_df = allow_df[allow_df['allow_content'].notna() & (allow_df['allow_content'].str.strip() != '')]\n", "allow_df = allow_df.groupby('company_id')['allow_content'].agg(list).reset_index()"]}, {"cell_type": "code", "execution_count": 12, "id": "b66b09fa", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_df, allow_df, left_on='lc_company_id', right_on='company_id', how='left')\n", "merge_df.drop(columns=['company_id'], inplace=True)\n", "del allow_df"]}, {"cell_type": "code", "execution_count": 13, "id": "6879a43a", "metadata": {}, "outputs": [], "source": ["# 读取company_app_filtered文件夹下的所有parquet文件，然后合并成一个df\n", "app_files = [f for f in os.listdir('company_app_filtered') if f.endswith('.parquet')]\n", "app_df = pd.concat([pd.read_parquet(os.path.join('company_app_filtered', f)) for f in app_files], ignore_index=True)"]}, {"cell_type": "code", "execution_count": 16, "id": "5582f788", "metadata": {}, "outputs": [], "source": ["# 清除app_brief列为None或者空字符串的行，然后根据company_id进行分组合并，对于同一个company_id的app_brief以列表形式组合\n", "app_df = app_df[app_df['app_brief'].notna() & (app_df['app_brief'].str.strip() != '')]\n", "app_df = app_df.groupby('company_id')['app_brief'].agg(list).reset_index()"]}, {"cell_type": "code", "execution_count": 18, "id": "c88a5588", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_df, app_df, left_on='lc_company_id', right_on='company_id', how='left')\n", "merge_df.drop(columns=['company_id'], inplace=True)\n", "del app_df"]}, {"cell_type": "code", "execution_count": null, "id": "764f8212", "metadata": {}, "outputs": [], "source": ["# 读取company_competitor_filtered文件夹下的所有parquet文件，然后合并成一个df\n", "competitor_files = [f for f in os.listdir('company_competitor_filtered') if f.endswith('.parquet')]\n", "competitor_df = pd.concat([pd.read_parquet(os.path.join('company_competitor_filtered', f)) for f in competitor_files], ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "63dd7eab", "metadata": {}, "outputs": [], "source": ["# 清除jingpin_yewu列为None或者空字符串的行，然后根据company_id进行分组合并，对于同一个company_id的jingpin_yewu以列表形式组合\n", "competitor_df = competitor_df[competitor_df['jingpin_yewu'].notna() & (competitor_df['jingpin_yewu'].str.strip() != '')]\n", "competitor_df = competitor_df.groupby('company_id')['jingpin_yewu'].agg(list).reset_index()"]}, {"cell_type": "code", "execution_count": 24, "id": "18bd36b9", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_df, competitor_df, left_on='lc_company_id', right_on='company_id', how='left')\n", "merge_df.drop(columns=['company_id'], inplace=True)\n", "del competitor_df"]}, {"cell_type": "code", "execution_count": 26, "id": "2c43ab46", "metadata": {}, "outputs": [], "source": ["# 读取company_investment_filtered文件夹下的所有parquet文件，然后合并成一个df\n", "investment_files = [f for f in os.listdir('company_investment_filtered') if f.endswith('.parquet')]\n", "investment_df = pd.concat([pd.read_parquet(os.path.join('company_investment_filtered', f)) for f in investment_files], ignore_index=True)"]}, {"cell_type": "code", "execution_count": 28, "id": "7abbe967", "metadata": {}, "outputs": [], "source": ["# 清除product_intro列为None或者空字符串的行，然后根据company_id进行分组合并，对于同一个company_id的product_intro以列表形式组合\n", "investment_df = investment_df[investment_df['product_intro'].notna() & (investment_df['product_intro'].str.strip() != '')]\n", "investment_df = investment_df.groupby('company_id')['product_intro'].agg(list).reset_index()\n"]}, {"cell_type": "code", "execution_count": 30, "id": "3a970e5f", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_df, investment_df, left_on='lc_company_id', right_on='company_id', how='left')\n", "merge_df.drop(columns=['company_id'], inplace=True)\n", "del investment_df"]}, {"cell_type": "code", "execution_count": 31, "id": "d326aa41", "metadata": {}, "outputs": [], "source": ["# 读取company_land_filtered文件夹下的所有parquet文件，然后合并成一个df\n", "land_files = [f for f in os.listdir('company_land_filtered') if f.endswith('.parquet')]\n", "land_df = pd.concat([pd.read_parquet(os.path.join('company_land_filtered', f)) for f in land_files], ignore_index=True)"]}, {"cell_type": "code", "execution_count": 33, "id": "fd94b253", "metadata": {}, "outputs": [], "source": ["# 清除land_project_name列为None或者空字符串的行，然后根据company_id进行分组合并，对于同一个company_id的land_project_name以列表形式组合\n", "land_df = land_df[land_df['land_project_name'].notna() & (land_df['land_project_name'].str.strip() != '')]\n", "land_df = land_df.groupby('company_id')['land_project_name'].agg(list).reset_index()"]}, {"cell_type": "code", "execution_count": 35, "id": "7ddc126f", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_df, land_df, left_on='lc_company_id', right_on='company_id', how='left')\n", "merge_df.drop(columns=['company_id'], inplace=True)\n", "del land_df"]}, {"cell_type": "markdown", "id": "e426e3a8", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 36, "id": "6acb214a", "metadata": {}, "outputs": [], "source": ["# 读取company_patent_filtered文件夹下的所有parquet文件，然后合并成一个df\n", "patent_files = [f for f in os.listdir('company_patent_filtered') if f.endswith('.parquet')]\n", "patent_df = pd.concat([pd.read_parquet(os.path.join('company_patent_filtered', f)) for f in patent_files], ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "27eaef7e", "metadata": {}, "outputs": [], "source": ["# 清除patent_abs, patent_title列同时为None或者空字符串的行，然后根据company_id进行分组合并，对于同一个company_id的patent_abs, patent_title以列表形式组合\n", "patent_df = patent_df[patent_df['patent_abs'].notna() & (patent_df['patent_abs'].str.strip() != '') & patent_df['patent_title'].notna() & (patent_df['patent_title'].str.strip() != '')]\n", "patent_df = patent_df.groupby('lc_company_id').agg({\n", "    'patent_abs': lambda x: [v for v in x if pd.notna(v) and str(v).strip() != ''],\n", "    'patent_title': lambda x: [v for v in x if pd.notna(v) and str(v).strip() != ''],\n", "}).reset_index()\n"]}, {"cell_type": "code", "execution_count": 44, "id": "1b534d50", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_df, patent_df, left_on='lc_company_id', right_on='lc_company_id', how='left')\n", "del patent_df"]}, {"cell_type": "code", "execution_count": 46, "id": "32ca21f5", "metadata": {}, "outputs": [], "source": ["merge_df = merge_df.sort_values(by='lc_company_id', ascending=True)"]}, {"cell_type": "code", "execution_count": 51, "id": "f1f7c3c5", "metadata": {}, "outputs": [], "source": ["# 计算需要分成多少批次\n", "batch_size = 100000\n", "total_batches = len(merge_df) // batch_size + (1 if len(merge_df) % batch_size != 0 else 0)\n", "\n", "# 创建保存目录\n", "save_dir = 'result_date_part01'\n", "if not os.path.exists(save_dir):\n", "    os.makedirs(save_dir)\n", "\n", "# 按批次保存数据\n", "for i in range(total_batches):\n", "    start_idx = i * batch_size\n", "    end_idx = min((i + 1) * batch_size, len(merge_df))\n", "    \n", "    # 获取当前批次的数据\n", "    batch_df = merge_df.iloc[start_idx:end_idx]\n", "    \n", "    # 生成文件名\n", "    file_name = f'{save_dir}/batch_{i+1:03d}.parquet'\n", "    \n", "    # 保存为parquet文件\n", "    batch_df.to_parquet(file_name, index=False)\n"]}, {"cell_type": "code", "execution_count": null, "id": "1e72e689", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "63b127ba", "metadata": {}, "source": ["## Part02"]}, {"cell_type": "code", "execution_count": 2, "id": "da308730", "metadata": {}, "outputs": [], "source": ["# 读取basic_info和business_scope文件夹下的所有parquet文件，并且分别合并成单独的两个df\n", "# 定义文件夹路径\n", "basic_info_path = 'company_basic_info'\n", "\n", "# 读取并合并basic_info文件夹下的所有parquet文件\n", "basic_info_files = [f for f in os.listdir(basic_info_path) if f.endswith('.parquet')]\n", "basic_info_df = pd.concat([pd.read_parquet(os.path.join(basic_info_path, f)) for f in basic_info_files], ignore_index=True)"]}, {"cell_type": "code", "execution_count": 3, "id": "d2007f4e", "metadata": {}, "outputs": [], "source": ["# 读取company_profile_filtered文件夹下的所有parquet文件，然后合并成一个df\n", "profile_files = [f for f in os.listdir('company_profile_filtered') if f.endswith('.parquet')]\n", "profile_df = pd.concat([pd.read_parquet(os.path.join('company_profile_filtered', f)) for f in profile_files], ignore_index=True)\n", "\n", "# 对于每个company_id，当存在多条记录时，只保留update_time最新的记录\n", "profile_df = profile_df.sort_values(by='update_time', ascending=False)\n", "profile_df = profile_df.drop_duplicates(subset='company_id', keep='first')\n", "\n", "# 清除company_profile列为None或者空字符串的行，然后根据company_id进行分组合并，对于同一个company_id的company_profile以列表形式组合\n", "profile_df = profile_df[profile_df['company_profile'].notna() & (profile_df['company_profile'].str.strip() != '')]\n", "profile_df = profile_df.groupby('company_id')['company_profile'].agg(list).reset_index()"]}, {"cell_type": "code", "execution_count": 5, "id": "b2c40688", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(basic_info_df, profile_df, left_on='lc_company_id', right_on='company_id', how='left')\n", "merge_df.drop(columns=['company_id'], inplace=True)"]}, {"cell_type": "code", "execution_count": 6, "id": "477076b0", "metadata": {}, "outputs": [], "source": ["del basic_info_df\n", "del profile_df"]}, {"cell_type": "code", "execution_count": 8, "id": "62ba1dd7", "metadata": {}, "outputs": [], "source": ["# 读取company_software_filtered文件夹下的所有parquet文件，然后合并成一个df\n", "software_files = [f for f in os.listdir('company_software_filtered') if f.endswith('.parquet')]\n", "software_df = pd.concat([pd.read_parquet(os.path.join('company_software_filtered', f)) for f in software_files], ignore_index=True)\n", "\n", "# 清除software_full_name列为None或者空字符串的行，然后根据company_id进行分组合并，对于同一个company_id的software_full_name以列表形式组合\n", "software_df = software_df[software_df['software_full_name'].notna() & (software_df['software_full_name'].str.strip() != '')]\n", "software_df = software_df.groupby('lc_company_id')['software_full_name'].agg(list).reset_index()"]}, {"cell_type": "code", "execution_count": 11, "id": "5778fca7", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_df, software_df, left_on='lc_company_id', right_on='lc_company_id', how='left')"]}, {"cell_type": "code", "execution_count": 12, "id": "f95c7a81", "metadata": {}, "outputs": [], "source": ["del software_df"]}, {"cell_type": "code", "execution_count": 28, "id": "6e0a4025", "metadata": {}, "outputs": [], "source": ["# 读取extracted_bid_winners文件夹下的所有parquet文件，然后合并成一个df\n", "bid_files = [f for f in os.listdir('extracted_bid_winners') if f.endswith('.parquet')]\n", "bid_df = pd.concat([pd.read_parquet(os.path.join('extracted_bid_winners', f)) for f in bid_files], ignore_index=True)\n", "\n", "# 清除title列为None或者空字符串的行，然后根据bid_win进行分组合并，对于同一个bid_win的title以列表形式组合\n", "bid_df = bid_df[bid_df['title'].notna() & (bid_df['title'].str.strip() != '')]\n", "# 处理company_name列，删除第一个汉字之前的字符\n", "def clean_company_name(name):\n", "    if isinstance(name, str):\n", "        # 查找第一个汉字的位置\n", "        for i, char in enumerate(name):\n", "            if '\\u4e00' <= char <= '\\u9fff':  # 判断是否为汉字\n", "                if i > 0:  # 如果第一个汉字不是在开头\n", "                    return name[i:]  # 删除第一个汉字之前的所有字符\n", "                break\n", "    return name\n", "\n", "# 应用清洗函数\n", "bid_df['bid_win'] = bid_df['bid_win'].apply(clean_company_name)\n", "bid_df = bid_df.groupby('bid_win')['title'].agg(list).reset_index()\n", "bid_df = bid_df.rename(columns={'bid_win': 'company_name', 'title': 'bid_title'})\n"]}, {"cell_type": "code", "execution_count": 30, "id": "38d9c654", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_df, bid_df, left_on='company_name', right_on='company_name', how='left')"]}, {"cell_type": "code", "execution_count": 32, "id": "509cb8bc", "metadata": {}, "outputs": [], "source": ["# 读取main_business_filtered文件夹下的所有parquet文件，然后合并成一个df\n", "yewu_files = [f for f in os.listdir('main_business_filtered') if f.endswith('.parquet')]\n", "yewu_df = pd.concat([pd.read_parquet(os.path.join('main_business_filtered', f)) for f in yewu_files], ignore_index=True)\n", "\n", "# 清除yewu, portray列同时为None或者空字符串的行，然后根据company_id进行分组合并，对于同一个company_id的yewu, portray以列表形式组合\n", "yewu_df = yewu_df[yewu_df['yewu'].notna() & (yewu_df['yewu'].str.strip() != '') & yewu_df['portray'].notna() & (yewu_df['portray'].str.strip() != '')]\n", "yewu_df = yewu_df.groupby('company_id').agg({\n", "    'yewu': lambda x: [v for v in x if pd.notna(v) and str(v).strip() != ''],\n", "    'portray': lambda x: [v for v in x if pd.notna(v) and str(v).strip() != ''],\n", "}).reset_index()"]}, {"cell_type": "code", "execution_count": 34, "id": "57065297", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_df, yewu_df, left_on='lc_company_id', right_on='company_id', how='left')\n", "merge_df.drop(columns=['company_id'], inplace=True)"]}, {"cell_type": "code", "execution_count": 36, "id": "b218454b", "metadata": {}, "outputs": [], "source": ["# 读取stock_company_filtered文件夹下的所有parquet文件，然后合并成一个df\n", "stock_files = [f for f in os.listdir('stock_company_filtered') if f.endswith('.parquet')]\n", "stock_df = pd.concat([pd.read_parquet(os.path.join('stock_company_filtered', f)) for f in stock_files], ignore_index=True)\n", "\n", "# 清除introduction, main_product, stock_business_scope列同时为None或者空字符串的行，然后根据company_id进行分组合并，对于同一个company_id的yewu, portray以列表形式组合\n", "stock_df = stock_df[stock_df['introduction'].notna() & (stock_df['introduction'].str.strip() != '') & stock_df['main_product'].notna() & (stock_df['main_product'].str.strip() != '') & stock_df['stock_business_scope'].notna() & (stock_df['stock_business_scope'].str.strip() != '')]\n", "stock_df = stock_df.groupby('company_id').agg({\n", "    'introduction': lambda x: [v for v in x if pd.notna(v) and str(v).strip() != ''],\n", "    'main_product': lambda x: [v for v in x if pd.notna(v) and str(v).strip() != ''],\n", "    'stock_business_scope': lambda x: [v for v in x if pd.notna(v) and str(v).strip() != ''],\n", "}).reset_index()\n", "stock_df.rename(columns={'company_id': 'lc_company_id', 'introduction': 'stock_introduction', 'main_product': 'stock_main_product'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 38, "id": "bb367651", "metadata": {}, "outputs": [], "source": ["merge_df = pd.merge(merge_df, stock_df, left_on='lc_company_id', right_on='lc_company_id', how='left')"]}, {"cell_type": "code", "execution_count": 39, "id": "05023537", "metadata": {}, "outputs": [], "source": ["merge_df = merge_df.sort_values(by='lc_company_id', ascending=True)\n", "\n", "# 计算需要分成多少批次\n", "batch_size = 100000\n", "total_batches = len(merge_df) // batch_size + (1 if len(merge_df) % batch_size != 0 else 0)\n", "\n", "# 创建保存目录\n", "save_dir = 'result_date_part02'\n", "if not os.path.exists(save_dir):\n", "    os.makedirs(save_dir)\n", "\n", "# 按批次保存数据\n", "for i in range(total_batches):\n", "    start_idx = i * batch_size\n", "    end_idx = min((i + 1) * batch_size, len(merge_df))\n", "    \n", "    # 获取当前批次的数据\n", "    batch_df = merge_df.iloc[start_idx:end_idx]\n", "    \n", "    # 生成文件名\n", "    file_name = f'{save_dir}/batch_{i+1:03d}.parquet'\n", "    \n", "    # 保存为parquet文件\n", "    batch_df.to_parquet(file_name, index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "c3607aa9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py10", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}