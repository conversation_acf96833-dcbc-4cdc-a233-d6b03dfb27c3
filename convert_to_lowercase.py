#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字母小写转换脚本
对result_date_part01_update和result_date_part02_update文件夹中的所有字段进行字母小写转换
输出到result_date_part01_update2和result_date_part02_update2文件夹
"""

import os
import pandas as pd
from tqdm import tqdm
import glob
import numpy as np


def convert_to_lowercase(value):
    """
    将字段中的所有字母转换为小写
    支持字符串、列表、numpy数组等类型
    """
    try:
        # 先检查是否是None或空字符串
        if value is None or value == '':
            return value

        # 对于单个值，检查是否是NaN
        if not isinstance(value, (list, tuple, np.ndarray)):
            if pd.isna(value):
                return value

        # 检查是否是numpy数组
        if isinstance(value, np.ndarray):
            result = []
            for item in value:
                if item is None or item == '':
                    result.append(item)
                elif pd.isna(item):
                    result.append(item)
                else:
                    result.append(str(item).lower())
            return np.array(result, dtype=object)

        # 检查是否是列表
        elif isinstance(value, list):
            result = []
            for item in value:
                if item is None or item == '':
                    result.append(item)
                elif pd.isna(item):
                    result.append(item)
                else:
                    result.append(str(item).lower())
            return result

        # 检查是否是元组
        elif isinstance(value, tuple):
            result = []
            for item in value:
                if item is None or item == '':
                    result.append(item)
                elif pd.isna(item):
                    result.append(item)
                else:
                    result.append(str(item).lower())
            return tuple(result)

        # 字符串处理
        else:
            return str(value).lower()

    except Exception as e:
        print(f"    转换小写时出错: {e}")
        return value


def process_dataframe(df, folder_name):
    """
    处理DataFrame中的数据，将指定字段的字母转换为小写
    """
    try:
        # 根据不同的文件夹确定排除的列
        if 'part01' in folder_name:
            excluded_columns = ['lc_company_id', 'company_name', 'company_status_clean']
        elif 'part02' in folder_name:
            excluded_columns = ['lc_company_id', 'company_name', 'company_status_clean', 'industry_l1_code']
        else:
            # 默认排除列
            excluded_columns = ['lc_company_id', 'company_name', 'company_status_clean', 'industry_l1_code']
        
        # 获取需要处理的列
        columns_to_process = [col for col in df.columns if col not in excluded_columns]
        
        print(f"  需要处理的字段: {columns_to_process}")
        
        # 创建副本以避免修改原数据
        df_processed = df.copy()
        
        # 对所有需要处理的字段进行字母小写转换
        for col in columns_to_process:
            if col in df_processed.columns:
                print(f"    转换字母为小写: {col}")
                df_processed[col] = df_processed[col].apply(convert_to_lowercase)
        
        return df_processed
    
    except Exception as e:
        print(f"    处理DataFrame时出错: {str(e)}")
        return df


def process_folder(input_folder, output_folder):
    """
    处理指定文件夹中的所有parquet文件
    """
    print(f"\n=== 处理文件夹: {input_folder} ===")
    
    # 检查输入文件夹是否存在
    if not os.path.exists(input_folder):
        print(f"错误：输入文件夹 '{input_folder}' 不存在！")
        return False
    
    # 创建输出文件夹
    os.makedirs(output_folder, exist_ok=True)
    print(f"输出文件夹已创建: {output_folder}")
    
    # 获取所有parquet文件
    parquet_files = glob.glob(os.path.join(input_folder, "*.parquet"))
    
    if not parquet_files:
        print(f"在 '{input_folder}' 文件夹中未找到任何 .parquet 文件！")
        return False
    
    print(f"找到 {len(parquet_files)} 个 .parquet 文件")
    
    # 处理每个文件
    success_count = 0
    for file_path in tqdm(parquet_files, desc=f"处理{os.path.basename(input_folder)}"):
        try:
            file_name = os.path.basename(file_path)
            output_path = os.path.join(output_folder, file_name)
            
            # 读取原始数据
            df = pd.read_parquet(file_path)
            
            # 处理数据
            df_processed = process_dataframe(df, input_folder)
            
            # 保存处理后的数据
            df_processed.to_parquet(output_path, index=False)
            success_count += 1
            
        except Exception as e:
            print(f"\n处理文件 {file_path} 时出错: {str(e)}")
            continue
    
    print(f"文件夹 {input_folder} 处理完成！成功处理 {success_count}/{len(parquet_files)} 个文件")
    return True


def main():
    """
    主函数
    """
    print("开始字母小写转换处理...")
    
    # 定义输入输出文件夹对
    folder_pairs = [
        ("result_date_part01_update", "result_date_part01_update2"),
        ("result_date_part02_update", "result_date_part02_update2")
    ]
    
    total_success = 0
    
    for input_folder, output_folder in folder_pairs:
        success = process_folder(input_folder, output_folder)
        if success:
            total_success += 1
    
    print(f"\n=== 处理完成 ===")
    print(f"成功处理 {total_success}/{len(folder_pairs)} 个文件夹")
    
    # 验证处理结果
    print("\n=== 验证处理结果 ===")
    for input_folder, output_folder in folder_pairs:
        if os.path.exists(output_folder):
            try:
                # 检查第一个文件
                test_files = glob.glob(os.path.join(output_folder, "batch_001.parquet"))
                if test_files:
                    df_test = pd.read_parquet(test_files[0])
                    print(f"{output_folder}: 数据形状 {df_test.shape}, 列数 {len(df_test.columns)}")
                else:
                    print(f"{output_folder}: 未找到测试文件")
            except Exception as e:
                print(f"{output_folder}: 验证时出错 - {str(e)}")


if __name__ == "__main__":
    main()
