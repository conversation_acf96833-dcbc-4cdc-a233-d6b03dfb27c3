"""
企业行政许可信息提取脚本
功能：从t_company_allow数据中提取指定企业的行政许可信息
"""

import pandas as pd
import os
from pathlib import Path
import gc

def extract_company_allow_info():
    """提取企业行政许可信息"""
    
    # 数据源路径
    basic_info_dir = Path("company_business_scope_output/company_basic_info")
    allow_source_dir = Path(r"F:\蕾奥工作\20.数据库转到本地\output\t_company_allow")
    
    # 输出路径
    output_dir = Path("company_allow_filtered")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 步骤1：读取并合并所有基本信息文件
    print("正在读取企业基本信息...")
    basic_info_files = list(basic_info_dir.glob("*.parquet"))
    print(f"找到 {len(basic_info_files)} 个基本信息文件")
    
    # 合并所有基本信息文件，只需要lc_company_id
    company_ids_list = []
    for file_path in basic_info_files:
        df = pd.read_parquet(file_path, columns=['lc_company_id'])
        company_ids_list.append(df)
    
    # 合并并去重
    target_company_ids = pd.concat(company_ids_list, ignore_index=True)['lc_company_id'].unique()
    target_company_ids_set = set(target_company_ids)
    
    print(f"目标企业ID数量: {len(target_company_ids_set):,}")
    
    # 清理临时数据
    del company_ids_list, target_company_ids
    gc.collect()
    
    # 步骤2：处理行政许可文件
    allow_files = list(allow_source_dir.glob("*.parquet"))
    print(f"找到 {len(allow_files)} 个行政许可文件")
    
    # 初始化变量
    batch_data = []
    batch_count = 0
    total_processed = 0
    batch_size_limit = 1000000  # 100万条记录
    
    # 需要的列
    required_columns = ['company_id', 'allow_content']
    
    for file_path in allow_files:
        print(f"正在处理文件: {file_path.name}")
        
        try:
            # 读取行政许可文件，只保留需要的列
            df = pd.read_parquet(file_path, columns=required_columns)
            
            batch_data.append(df)
            total_processed += len(df)
            print(f"  - 当前文件记录数: {len(df)}, 累计: {total_processed}")
            
            # 检查是否达到批次大小限制
            if total_processed >= batch_size_limit:
                batch_count += 1
                process_and_save_allow_batch(batch_data, batch_count, target_company_ids_set, output_dir)
                
                # 清理内存
                del batch_data
                gc.collect()
                
                # 重置变量
                batch_data = []
                total_processed = 0
                print(f"批次 {batch_count} 处理完成并清理内存")
            
            # 清理当前文件数据
            del df
            gc.collect()
            
        except Exception as e:
            print(f"处理文件 {file_path.name} 时出错: {e}")
            continue
    
    # 处理剩余数据
    if batch_data and total_processed > 0:
        batch_count += 1
        process_and_save_allow_batch(batch_data, batch_count, target_company_ids_set, output_dir)
        print(f"最后批次 {batch_count} 处理完成")
    
    print(f"企业行政许可信息提取完成，共处理 {batch_count} 个批次")

def process_and_save_allow_batch(batch_data_list, batch_num, target_company_ids_set, output_dir):
    """处理并保存批次数据"""
    
    # 合并批次数据
    combined_df = pd.concat(batch_data_list, ignore_index=True)
    print(f"  批次 {batch_num} 合并后记录数: {len(combined_df)}")
    
    # 筛选目标企业的记录（使用company_id列）
    filtered_df = combined_df[combined_df['company_id'].isin(target_company_ids_set)].copy()
    
    if len(filtered_df) > 0:
        # 保存筛选后的数据
        output_file = output_dir / f"company_allow_batch_{batch_num:03d}.parquet"
        filtered_df.to_parquet(output_file, index=False)
        print(f"  批次 {batch_num} 保存完成: {output_file} ({len(filtered_df)} 条记录)")
    else:
        print(f"  批次 {batch_num} 筛选后无记录，跳过保存")
    
    # 清理临时数据
    del combined_df, filtered_df
    gc.collect()

if __name__ == "__main__":
    print("开始提取企业行政许可信息...")
    extract_company_allow_info()
    print("提取完成！")